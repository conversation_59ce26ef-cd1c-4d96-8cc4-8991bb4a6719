* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    min-height: 100vh;
    padding: 20px;
    overflow-x: hidden;
}

.container {
    max-width: 100%;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

h1 {
    text-align: center;
    color: #00d4ff;
    font-size: 3rem;
    font-weight: 300;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    margin-bottom: 10px;
}

.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-box {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-box:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #00d4ff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.mode-selector {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.mode-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.2);
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

}

.mode-btn.active {
    background: #00d4ff;
    color: #0f0f23;
    border-color: #00d4ff;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.mode-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#fingerSettings {
    display: none;
}


.setting-label {
    font-weight: 600;
    color: #00d4ff;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.setting-control {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.setting-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.setting-btn.active {
    background: #00d4ff;
    color: #0f0f23;
    border-color: #00d4ff;
}

.setting-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.text-display {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 20px;
    font-size: 1.8rem;
    line-height: 2.2;
    font-family: 'Courier New', monospace;
    text-align: left;
    letter-spacing: 0.05em;
    word-wrap: break-word;
    overflow-wrap: break-word;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    max-height: 250px;
    overflow-y: auto;
}


.char {
    position: relative;
    padding: 3px 2px;
    margin: 0 1px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.char.correct {
    background-color: rgba(0, 255, 127, 0.3);
    color: #00ff7f;
}

.char.incorrect {
    background-color: rgba(255, 69, 58, 0.3);
    color: #ff453a;
}

.char.current {
    background-color: #00d4ff;
    color: #0f0f23;
    animation: pulse 1.5s infinite;
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

.input-area {
    width: 100%;
    padding: 25px;
    font-size: 1.2rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    transition: all 0.3s ease;
    margin-bottom: 20px;
    min-height: 80px;
    resize: vertical;
}

.input-area:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.input-area::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.controls {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.btn {
    background: linear-gradient(135deg, #00d4ff, #0099cc);
    color: #0f0f23;
    border: none;
    padding: 15px 35px;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
}

.btn:active {
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    margin-bottom: 20px;
    overflow: hidden;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #00ff7f);
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.results {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 40px;
    margin-top: 20px;
    text-align: center;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    border: 1px solid rgba(255, 255, 255, 0.1);
}

.results.show {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-title {
    font-size: 2rem;
    color: #00d4ff;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.result-stat {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.result-stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.result-stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #00d4ff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.difficulty-indicator {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    font-weight: 500;
    color: #00d4ff;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    border: 1px solid rgba(255, 255, 255, 0.1);
}

.finger-guide {
    margin-top: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    display: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);

    border: 1px solid rgba(255, 255, 255, 0.1);
}

.finger-guide.show {
    display: block;
}

.finger-info {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.2rem;
    color: #00d4ff;
}

.keyboard-layout {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 8px;
    margin-top: 20px;
    font-family: 'Courier New', monospace;
}

.key {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 8px;
    text-align: center;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.key.highlighted {
    background: #00d4ff;
    color: #0f0f23;
    border-color: #00d4ff;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    h1 {
        font-size: 2rem;
    }

    .text-display {
        font-size: 1.4rem;
        padding: 25px;
    }

    .stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .settings {
        grid-template-columns: 1fr;
    }
}