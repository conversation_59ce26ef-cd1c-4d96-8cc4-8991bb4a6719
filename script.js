// Basic vocabulary pool for constructing new words/sentences
const nouns = ["cat", "ocean", "dream", "forest", "river", "fire", "star", "path", "storm", "mind"];
const verbs = ["runs", "flows", "shines", "whispers", "glows", "fades", "moves", "calls", "rises"];
const adjectives = ["silent", "bright", "cold", "dark", "ancient", "gentle", "wild", "hidden"];
const adverbs = ["quickly", "softly", "silently", "suddenly", "peacefully", "brightly"];
const connectors = ["and", "but", "because", "while", "as"];

// Generate semi-random meaningful sentences
function generateMeaningfulSentence() {
    const subject = `The ${adjectives[Math.floor(Math.random() * adjectives.length)]} ${nouns[Math.floor(Math.random() * nouns.length)]}`;
    const verbPhrase = `${verbs[Math.floor(Math.random() * verbs.length)]} ${adverbs[Math.floor(Math.random() * adverbs.length)]}`;
    const connector = connectors[Math.floor(Math.random() * connectors.length)];
    const object = `the ${adjectives[Math.floor(Math.random() * adjectives.length)]} ${nouns[Math.floor(Math.random() * nouns.length)]}`;
    return `${subject} ${verbPhrase} ${connector} ${object}.`;
}

// Text generator functions for different typing modes
const generators = {
    // Random mode: generates text with letters, numbers, and punctuation
    random: () => {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.,!?;: ';
        return generateRandomWords(chars);
    },

    // Letters mode: generates text with only lowercase letters
    letters: () => {
        const letters = 'abcdefghijklmnopqrstuvwxyz';
        return generateRandomWords(letters);
    },

    // Finger mode: generates text based on specific finger training
    finger: () => {
        // Character mappings for each finger on QWERTY keyboard
        const fingerMaps = {
            all: 'abcdefghijklmnopqrstuvwxyz',
            pinky: 'qpaz',           // Left pinky keys
            ring: 'woslx',           // Ring finger keys
            middle: 'edcik',         // Middle finger keys
            index: 'rfvtgbyhun'      // Index finger keys
        };
        const chars = fingerMaps[currentFinger] || fingerMaps.all;
        return generateRandomWords(chars);
    },

    // Words mode: generates text using real vocabulary words
    words: () => {
        const allWords = [...nouns, ...verbs, ...adjectives, ...adverbs];
        const result = [];
        for (let i = 0; i < currentLength; i++) {
            result.push(allWords[Math.floor(Math.random() * allWords.length)]);
        }
        return result.join(" ");
    },

    // Sentences mode: generates meaningful sentences using vocabulary
    sentences: () => {
        const result = [];
        let count = 0;
        // Generate sentences until we reach the target word count
        while (count < currentLength) {
            const sentence = generateMeaningfulSentence();
            result.push(sentence);
            count += sentence.split(" ").length;
        }
        return result.join(" ");
    }
};


// Helper function to generate random words from a given character set
function generateRandomWords(charSet) {
    const words = [];
    // Generate the specified number of words
    for (let i = 0; i < currentLength; i++) {
        // Each word is 2-6 characters long
        const wordLength = Math.floor(Math.random() * 5) + 2;
        let word = '';
        // Build word character by character from the character set
        for (let j = 0; j < wordLength; j++) {
            word += charSet[Math.floor(Math.random() * charSet.length)];
        }
        words.push(word);
    }
    return words.join(' ');
}

// Global variables for test state and configuration
let currentText = '';           // The text currently being typed
let startTime = 0;              // Timestamp when test started
let timerInterval = null;       // Reference to the countdown timer
let testDuration = 60;          // Test duration in seconds
let currentLength = 50;         // Number of words/characters to generate
let currentMode = 'random';     // Current typing mode (random, letters, finger, words, sentences)
let currentFinger = 'all';      // Current finger training mode
let isTestActive = false;       // Whether a test is currently running

// Real-time typing statistics
let correctChars = 0;           // Number of correctly typed characters
let totalChars = 0;             // Total number of characters typed
let errors = 0;                 // Number of typing errors

// DOM element references for UI manipulation
const textDisplay = document.getElementById('textDisplay');           // Container for the text to be typed
const userInputEl = document.getElementById('userInput');             // Input field where user types
const startBtn = document.getElementById('startBtn');                 // Start test button
const resetBtn = document.getElementById('resetBtn');                 // Reset test button
const wpmEl = document.getElementById('wpm');                         // WPM display element
const accuracyEl = document.getElementById('accuracy');               // Accuracy display element
const timerEl = document.getElementById('timer');                     // Timer display element
const progressEl = document.getElementById('progress');               // Progress bar element
const resultsEl = document.getElementById('results');                 // Results section container
const fingerGuideEl = document.getElementById('fingerGuide');         // Finger position guide
const difficultyIndicatorEl = document.getElementById('difficultyIndicator'); // Mode description text

// Function to change typing mode (random, letters, finger, words, sentences)
function setMode(mode) {
    currentMode = mode;

    // Update active button styling
    document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Show/hide finger-specific settings
    document.getElementById('fingerSettings').style.display = mode === 'finger' ? 'block' : 'none';
    fingerGuideEl.classList.toggle('show', mode === 'finger');

    // Update mode description and generate new text
    updateDifficultyIndicator();
    if (!isTestActive) generateNew();
}

// Function to change test duration
function setDuration(duration) {
    testDuration = duration;

    // Update active button styling for duration buttons
    document.querySelectorAll('.setting-btn').forEach(btn => {
        if (btn.textContent.includes('s') || btn.textContent.includes('min')) {
            btn.classList.remove('active');
        }
    });
    event.target.classList.add('active');

    // Update timer display
    timerEl.textContent = duration + 's';
}

// Function to change the number of words/characters to generate
function setLength(length) {
    currentLength = length;

    // Update active button styling for length buttons (exclude duration and finger buttons)
    document.querySelectorAll('.setting-btn').forEach(btn => {
        if (!btn.textContent.includes('s') && !btn.textContent.includes('min') && !btn.textContent.includes('All') && !btn.textContent.includes('Pinky') && !btn.textContent.includes('Ring') && !btn.textContent.includes('Middle') && !btn.textContent.includes('Index')) {
            btn.classList.remove('active');
        }
    });
    event.target.classList.add('active');

    // Generate new text with updated length if test is not active
    if (!isTestActive) generateNew();
}

// Function to change finger training mode
function setFinger(finger) {
    currentFinger = finger;

    // Update active button styling for finger buttons
    document.querySelectorAll('#fingerSettings .setting-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Generate new text for the selected finger if test is not active
    if (!isTestActive) generateNew();
}

// Function to update the mode description text
function updateDifficultyIndicator() {
    const indicators = {
        random: `Random Mode - Generated using alphanumeric characters & symbols.`,
        letters: `Letters Only Mode - Great for key memorization.`,
        finger: `Finger Practice Mode - ${currentFinger} finger only.`,
        words: `Word Mode - Random letter-composed words.`
    };
    difficultyIndicatorEl.textContent = indicators[currentMode];
}

function generateNew() {
    // Generate new text based on current mode (letters, numbers, or words)
    currentText = generators[currentMode]();

    // Clear previous text display
    textDisplay.innerHTML = '';

    // Create individual span elements for each character to enable styling
    currentText.split('').forEach(char => {
        const span = document.createElement('span');
        span.textContent = char;
        span.classList.add('char');
        textDisplay.appendChild(span);
    });

    // Reset input field and UI elements for new test
    userInputEl.value = '';
    userInputEl.disabled = true;
    progressEl.style.width = '0%';
    resultsEl.classList.remove('show');
}

function startTest() {
    // Prevent starting multiple tests simultaneously
    if (isTestActive) return;

    // Set test state to active
    isTestActive = true;

    // Enable and focus the input field for user typing
    userInputEl.disabled = false;
    userInputEl.focus();

    // Record the test start time for calculating elapsed time
    startTime = Date.now();

    // Initialize typing statistics counters
    correctChars = 0;
    totalChars = 0;
    errors = 0;

    // Display initial timer value
    timerEl.textContent = testDuration + 's';

    // Start the countdown timer that updates every second
    timerInterval = setInterval(() => {
        // Calculate elapsed time in seconds
        const elapsed = Math.floor((Date.now() - startTime) / 1000);

        // Calculate remaining time
        const timeLeft = testDuration - elapsed;

        // Update timer display
        timerEl.textContent = timeLeft + 's';

        // Update progress bar based on elapsed time percentage
        const percent = (elapsed / testDuration) * 100;
        progressEl.style.width = percent + '%';

        // End test when time runs out
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            endTest();
        }
    }, 1000);
}

function resetTest() {
    // Stop any running timer
    clearInterval(timerInterval);

    // Set test state to inactive
    isTestActive = false;

    // Disable and clear the input field
    userInputEl.disabled = true;
    userInputEl.value = '';

    // Clear the text display area
    textDisplay.innerHTML = '';

    // Reset progress bar to 0%
    progressEl.style.width = '0%';

    // Reset timer display to initial value
    timerEl.textContent = testDuration + 's';

    // Reset statistics display to initial values
    wpmEl.textContent = '0';
    accuracyEl.textContent = '100%';

    // Hide results section
    resultsEl.classList.remove('show');

    // Generate new text for the next test
    generateNew();
}

function endTest() {
    // Set test state to inactive
    isTestActive = false;

    // Disable input field to prevent further typing
    userInputEl.disabled = true;

    // Calculate elapsed time in minutes for WPM calculation
    const timeElapsed = (Date.now() - startTime) / 1000 / 60; // in minutes

    // Calculate WPM (Words Per Minute) - standard formula: correct chars / 5 / minutes
    const wpm = Math.round((correctChars / 5) / timeElapsed);

    // Calculate accuracy percentage (correct chars / total chars * 100)
    const accuracy = Math.round((correctChars / totalChars) * 100) || 0;

    // Update live statistics display during test
    wpmEl.textContent = wpm;
    accuracyEl.textContent = accuracy + '%';

    // Update final results in the results modal/section
    document.getElementById('finalWpm').textContent = wpm;
    document.getElementById('finalAccuracy').textContent = accuracy + '%';
    document.getElementById('finalChars').textContent = totalChars;
    document.getElementById('finalErrors').textContent = errors;

    // Show the results section with animation
    resultsEl.classList.add('show');
}

// Event listener for user input during typing test
userInputEl.addEventListener('input', () => {
    // Only process input if test is currently active
    if (!isTestActive) return;

    // Get current user input and all character spans
    const input = userInputEl.value;
    const spans = textDisplay.querySelectorAll('.char');

    // Reset counters for real-time calculation
    correctChars = 0;
    totalChars = input.length;
    errors = 0;

    // Compare each typed character with the expected character
    spans.forEach((span, idx) => {
        const typedChar = input[idx];

        // Character not yet typed - reset to default style
        if (!typedChar) {
            span.className = 'char';
        }
        // Character matches expected - mark as correct
        else if (typedChar === span.textContent) {
            span.className = 'char correct';
            correctChars++;
        }
        // Character doesn't match - mark as incorrect
        else {
            span.className = 'char incorrect';
            errors++;
        }
    });

    // End test if user has typed all characters
    if (input.length >= currentText.length) {
        clearInterval(timerInterval);
        endTest();
    }
});

// Initialize the application by generating the first text
generateNew();
